[versions]
log4j = "2.24.3"
netty = "4.2.0.RC3"
expiringmap = "0.5.11"
network = "1.6.28-SNAPSHOT"
fastutil = "8.5.15"
lombok = "8.12.1"
math = "2.0"
nbt = "3.0.3.Final"
snappy = "2.0.2"
checkerframework = "0.6.49"
jose4j = "0.9.6"
minecraft-auth = "4.1.1"
jackson-databind = "2.18.2"
testng = "6.9.6"
junit-jupiter = "5.8.1"

[libraries]
log4j-bom = { group = "org.apache.logging.log4j", name = "log4j-bom", version.ref = "log4j" }
log4j-api = { group = "org.apache.logging.log4j", name = "log4j-api" }
log4j-core = { group = "org.apache.logging.log4j", name = "log4j-core" }
netty-common = { group = "io.netty", name = "netty-common", version.ref = "netty" }
netty-buffer = { group = "io.netty", name = "netty-buffer", version.ref = "netty" }
netty-codec = { group = "io.netty", name = "netty-codec", version.ref = "netty" }
netty-transport = { group = "io.netty", name = "netty-transport", version.ref = "netty" }
netty-transport-native-unix-common = { group = "io.netty", name = "netty-transport-native-unix-common", version.ref = "netty" }
expiringmap = { group = "net.jodah", name = "expiringmap", version.ref = "expiringmap" }
network-common = { group = "com.nukkitx.network", name = "common", version.ref = "network" }
fastutil-bom = { group = "org.cloudburstmc.fastutil", name = "bom", version.ref = "fastutil" }
fastutil-long-common = { group = "org.cloudburstmc.fastutil.commons", name = "long-common" }
fastutil-long-obj-maps = { group = "org.cloudburstmc.fastutil.maps", name = "long-object-maps" }
fastutil-int-obj-maps = { group = "org.cloudburstmc.fastutil.maps", name = "int-object-maps" }
fastutil-obj-int-maps = { group = "org.cloudburstmc.fastutil.maps", name = "object-int-maps" }
math = { group = "org.cloudburstmc.math", name = "immutable", version.ref = "math" }
nbt = { group = "org.cloudburstmc", name = "nbt", version.ref = "nbt" }
snappy = { group = "io.airlift", name = "aircompressor", version.ref = "snappy" }
jose4j = { group = "org.bitbucket.b_c", name = "jose4j", version.ref = "jose4j" }
minecraft-auth = { group = "net.raphimc", name = "MinecraftAuth", version.ref = "minecraft-auth" }
jackson-databind = { group = "com.fasterxml.jackson.core", name = "jackson-databind", version.ref = "jackson-databind" }
testng = { group = "org.testng", name = "testng", version.ref = "testng" }
junit-jupiter = { group = "org.junit.jupiter", name = "junit-jupiter", version.ref = "junit-jupiter" }

[bundles]
netty = [ "netty-common", "netty-buffer", "netty-codec", "netty-transport", "netty-transport-native-unix-common" ]

[plugins]
lombok = { id = "io.freefair.lombok", version.ref = "lombok" }
checkerframework = { id = "org.checkerframework", version.ref = "checkerframework" }