package com.mucheng.mucute.relay

import com.mucheng.mucute.relay.address.MuCuteAddress
import com.mucheng.mucute.relay.listener.MuCuteRelayPacketListener
import com.mucheng.mucute.relay.util.captureGamePacket
import org.cloudburstmc.protocol.bedrock.packet.BedrockPacket
import org.cloudburstmc.protocol.bedrock.packet.LoginPacket
import org.cloudburstmc.protocol.bedrock.packet.PlayStatusPacket
import org.cloudburstmc.protocol.bedrock.packet.StartGamePacket

/**
 * Simple test runner for MuCuteRelay that can be used in Android Studio
 * This bypasses complex logging and uses simple println statements
 */
object TestRunner {
    
    @JvmStatic
    fun main(args: Array<String>) {
        println("=== MuCuteRelay Test Runner ===")
        println("Protocol Version: ${MuCuteRelay.DefaultCodec.protocolVersion}")
        println("Minecraft Version: ${MuCuteRelay.DefaultCodec.minecraftVersion}")
        
        try {
            // Parse command line arguments
            val localPort = if (args.isNotEmpty()) args[0].toIntOrNull() ?: 19132 else 19132
            val remoteHost = if (args.size > 1) args[1] else "play.lbsg.net"
            val remotePort = if (args.size > 2) args[2].toIntOrNull() ?: 19132 else 19132
            
            println("Local Address: 0.0.0.0:$localPort")
            println("Remote Address: $remoteHost:$remotePort")
            println("Starting relay...")
            
            // Start the relay
            val relay = captureGamePacket(
                localAddress = MuCuteAddress("0.0.0.0", localPort),
                remoteAddress = MuCuteAddress(remoteHost, remotePort)
            ) {
                println("✓ Session created for client connection")
                
                // Add packet listener for debugging
                this.listeners.add(object : MuCuteRelayPacketListener {
                    override fun beforeClientBound(packet: BedrockPacket): Boolean {
                        when (packet) {
                            is LoginPacket -> println("📦 → Client: LoginPacket")
                            is PlayStatusPacket -> println("📦 → Client: PlayStatusPacket (status: ${packet.status})")
                            is StartGamePacket -> println("📦 → Client: StartGamePacket")
                            else -> {
                                // Only log important packets to avoid spam
                                if (packet.javaClass.simpleName.contains("Login") || 
                                    packet.javaClass.simpleName.contains("Status") ||
                                    packet.javaClass.simpleName.contains("Start")) {
                                    println("📦 → Client: ${packet.javaClass.simpleName}")
                                }
                            }
                        }
                        return false // Don't block the packet
                    }
                    
                    override fun afterClientBound(packet: BedrockPacket) {
                        // Do nothing
                    }
                    
                    override fun beforeServerBound(packet: BedrockPacket): Boolean {
                        when (packet) {
                            is LoginPacket -> println("📦 ← Server: LoginPacket")
                            else -> {
                                // Only log important packets to avoid spam
                                if (packet.javaClass.simpleName.contains("Login") || 
                                    packet.javaClass.simpleName.contains("Status") ||
                                    packet.javaClass.simpleName.contains("Start")) {
                                    println("📦 ← Server: ${packet.javaClass.simpleName}")
                                }
                            }
                        }
                        return false // Don't block the packet
                    }
                    
                    override fun afterServerBound(packet: BedrockPacket) {
                        // Do nothing
                    }
                    
                    override fun onDisconnect(reason: String) {
                        println("🔌 Client disconnected: $reason")
                    }
                })
            }
            
            println("✓ MuCuteRelay started successfully!")
            println("✓ Listening on port $localPort")
            println("✓ Forwarding to $remoteHost:$remotePort")
            println("Press Ctrl+C to stop...")
            
            // Keep the application running
            Runtime.getRuntime().addShutdownHook(Thread {
                println("Shutting down MuCuteRelay...")
                relay.disconnect()
                println("MuCuteRelay stopped.")
            })
            
            // Wait indefinitely
            while (relay.isRunning) {
                Thread.sleep(1000)
            }
            
        } catch (e: Exception) {
            println("✗ Failed to start MuCuteRelay: ${e.message}")
            e.printStackTrace()
            System.exit(1)
        }
    }
    
    /**
     * Test with specific server (for testing different servers)
     */
    fun testWithServer(serverHost: String, serverPort: Int = 19132, localPort: Int = 19132) {
        println("=== Testing with server: $serverHost:$serverPort ===")
        main(arrayOf(localPort.toString(), serverHost, serverPort.toString()))
    }
    
    /**
     * Test with Hive server
     */
    fun testHive() {
        testWithServer("geo.hivebedrock.network", 19132, 19132)
    }
    
    /**
     * Test with Cubecraft server
     */
    fun testCubecraft() {
        testWithServer("play.cubecraft.net", 19132, 19133)
    }
    
    /**
     * Test with local server
     */
    fun testLocal() {
        testWithServer("127.0.0.1", 19132, 19134)
    }
}
