package org.cloudburstmc.protocol.bedrock.data;

public enum BlockInteractionType {
    NONE,
    EXTEND,
    <PERSON><PERSON><PERSON><PERSON>,
    LOCK,
    CREATE,
    CREATE_LOCATOR,
    RENAM<PERSON>,
    ITEM_PLACED,
    ITEM_REMOVED,
    COOKING,
    DOUSING,
    LIGHTING,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    ADD_DYE,
    <PERSON><PERSON><PERSON>_ITEM,
    <PERSON><PERSON><PERSON>_ITEM,
    <PERSON><PERSON><PERSON>NT_ARROW,
    COMPOST_ITEM_PLACE,
    RECOVERED_BONEMEAL,
    BOOK_PLACED,
    BOOK_OPEN,
    DISENCHANT,
    REPA<PERSON>,
    DISENCHANT_AND_REPAIR
}
